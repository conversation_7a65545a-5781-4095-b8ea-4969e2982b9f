<template>
  <div class="divBox relative">
    <el-card class="box-card">
      <div class="container mt-1">
        <el-form v-model="searchFrom" inline size="small">
          <el-form-item :label="$t('chainTransferRecord.keyword') + '：'">
            <el-input
              v-model="searchFrom.keyword"
              :placeholder="$t('common.enter')"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item :label="$t('chainTransferRecord.brandName') + '：'">
            <el-select
              v-model="searchFrom.brandCode"
              :placeholder="$t('common.all')"
              clearable
            >
              <el-option
                v-for="item in brandList"
                :key="item.code"
                :label="item.name"
                :value="item.code"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>

      <el-button size="small" type="primary" class="mr10" @click="getList(1)">
        {{ $t("chainTransferRecord.query") }}
      </el-button>

      <el-button size="small" type="" class="mr10" @click="resetForm">
        {{ $t("chainTransferRecord.reset") }}
      </el-button>
    </el-card>

    <el-card class="box-card" style="margin-top: 12px">
      <div slot="header" class="clearfix">
        <el-button
          type="primary"
          size="small"
          v-hasPermi="['admin:financialCenter:request:upload']"
        >
          {{ $t("chainTransferRecord.exportExcel") }}
        </el-button>
      </div>
      <el-table
        v-loading="loading"
        :data="tableData"
        size="small"
        :header-cell-style="{ fontWeight: 'bold' }"
      >
        <el-table-column
          :label="$t('chainTransferRecord.serialNumber')"
          type="index"
          width="110"
        />
        <el-table-column :label="$t('chainTransferRecord.nickname')">
          <template slot-scope="scope">
            <span>{{ scope.row.userAccount | filterEmpty }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('chainTransferRecord.tiktokId')">
          <template slot-scope="scope">
            <span>{{ scope.row.tiktokUid | filterEmpty }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('chainTransferRecord.originalLink')">
          <template slot-scope="scope">
            <span>{{ scope.row.originUrl | filterEmpty }}</span>
          </template></el-table-column
        >
        <el-table-column :label="$t('chainTransferRecord.rebateLink')">
          <template slot-scope="scope">
            <span>{{ scope.row.shareUrl | filterEmpty }}</span>
          </template></el-table-column
        >
        <el-table-column :label="$t('chainTransferRecord.operationTime')">
          <template slot-scope="scope">
            <span>{{ scope.row.operateTime | filterEmpty }}</span>
          </template></el-table-column
        >
        <el-table-column :label="$t('chainTransferRecord.linkSource')">
          <template slot-scope="scope">
            <span>{{ scope.row.channel | filterEmpty }}</span>
          </template></el-table-column
        >
        <el-table-column :label="$t('chainTransferRecord.productId')">
          <template slot-scope="scope">
            <span>{{ scope.row.productId | filterEmpty }}</span>
          </template></el-table-column
        >
        <el-table-column :label="$t('chainTransferRecord.productName')">
          <template slot-scope="scope">
            <span>{{ scope.row.productName | filterEmpty }}</span>
          </template></el-table-column
        >
        <el-table-column :label="$t('chainTransferRecord.productPrice')">
          <template slot-scope="scope">
            <span>{{ scope.row.productPrice | filterEmpty }}</span>
          </template></el-table-column
        >
        <el-table-column :label="$t('chainTransferRecord.productCashbackRate')">
          <template slot-scope="scope">{{
            formatRate(scope.row.productCashbackRate)
          }}</template></el-table-column
        >
        <el-table-column :label="$t('chainTransferRecord.userCashbackRate')">
          <template slot-scope="scope">{{
            formatRate(scope.row.userCashbackRate)
          }}</template></el-table-column
        >
      </el-table>
      <el-pagination
        class="mt20"
        @size-change="sizeChange"
        @current-change="pageChange"
        :current-page="searchFrom.page"
        :page-sizes="[20, 40, 60, 100]"
        :page-size="searchFrom.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="searchFrom.total"
      >
      </el-pagination>
    </el-card>
  </div>
</template>

<script>
import { productShareRecordApi } from "@/api/chainTransferRecord";
import { brandLstApi } from "@/api/brand";
export default {
  name: "ChainTransferRecord",
  data () {
    return {
      loading: false,
      searchFrom: {
        keyword: "",
        brandCode: "",
        page: 1,
        limit: 20,
        total: 0
      },
      tableData: [],
      brandList: []
    };
  },
  created () { },
  mounted () {
    this.getList();
    this.getBrandList();
  },
  methods: {
    // 列表
    getList (num) {
      this.loading = true;

      this.searchFrom.page = num ? num : this.searchFrom.page;
      productShareRecordApi(this.searchFrom)
        .then(res => {
          this.tableData = res.list || [];
          this.searchFrom.total = res.total;
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
        });
    },

    getBrandList () {
      const params = {
        page: 1,
        limit: 999999,
        name: "",
        type: "-1"
      };
      brandLstApi(params)
        .then(res => {
          this.brandList = res.list;
        })
        .catch(res => {
          this.$message.error(this.$t("common.fetchDataFailed"));
        });
    },
    //切换页数
    pageChange (index) {
      this.searchFrom.page = index;
      this.getList();
    },
    //切换显示条数
    sizeChange (index) {
      this.searchFrom.limit = index;
      this.getList();
    },
    resetForm () {
      this.searchFrom = {
        keyword: "",
        brandCode: "",
        page: 1,
        limit: 20,
        total: 0
      };
      this.getList();
    },

    formatRate (s) {
      return parseInt(s * 10000) / 100 + "%";
    }
  }
};
</script>
<style scoped lang="scss">
/**/
</style>
