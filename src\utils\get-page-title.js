// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2021 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------

import defaultSettings from '@/settings'
import i18n from '../i18n'  // 引入i18n实例

const title = defaultSettings.title || 'Vue Element Admin'

export default function getPageTitle (pageTitle) {
  if (pageTitle) {
    return `${i18n.t('dashboard.' + pageTitle)} - ${title}`
  }
  return `${title}`
}